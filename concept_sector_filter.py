"""
概念和板块名称过滤器模块

该模块提供了过滤无意义概念和板块名称的功能，
可以被其他模块调用以清理概念和板块数据。

作者: 从 dynamic_gap_detector.py 中分离出来
功能: 过滤掉无意义的概念和板块名称，提升数据质量
"""


def filter_meaningful_concepts_and_sectors(concepts_or_sectors):
    """
    过滤掉无意义的概念和板块

    参数:
    - concepts_or_sectors: 概念或板块列表/字典

    返回:
    - 过滤后的概念或板块
    """
    # 定义需要过滤掉的无意义概念和板块
    meaningless_items = {
        '昨日涨停_含一字',
        '融资融券',
        '预盈预增',
        '含可转债',
        '转债标的',
        '央视50',
        '央视50_',  # 添加带下划线的变体
        '基金重仓',
        '微盘股',
        '创业板综',
        'QFII重仓',
        '央国企改革',
        '专精特新',
        '标准普尔',
        '低价股',
        '昨日涨停',
        '昨日触板',
        '一带一路',
        '高股息精选',
        '含H股',
        '通达信88',
        '含B股',
        '上证50',
        '上证50_',  # 新增
        '上证180',  # 新增
        'ST板块',
        '昨日连板_含一字',
        '昨日连板',
        '沪股通',
        '深股通',
        '港股通',
        'MSCI',
        '标普道琼斯',
        '富时罗素',
        '参股新三板',  # 添加参股新三板
        '证金持股',  # 新增
        '同花顺漂亮100',  # 新增
        '同花顺中特估100',  # 新增
        '预亏预减',  # 新增
        '机构重仓',  # 新增
        '中证500',  # 新增
        '深成500',  # 新增
        '贬值受益',  # 新增
        '小盘非融',  # 新增
        'AH股'  # 新增
    }

    if isinstance(concepts_or_sectors, dict):
        # 如果是字典（概念统计），过滤键
        return {k: v for k, v in concepts_or_sectors.items() if k not in meaningless_items}
    elif isinstance(concepts_or_sectors, list):
        # 如果是列表，过滤元素
        return [item for item in concepts_or_sectors if item not in meaningless_items]
    else:
        return concepts_or_sectors


def get_meaningless_items():
    """
    获取无意义概念和板块的集合
    
    返回:
    - set: 无意义概念和板块的集合
    """
    return {
        '昨日涨停_含一字',
        '融资融券',
        '预盈预增',
        '含可转债',
        '转债标的',
        '央视50',
        '央视50_',  # 添加带下划线的变体
        '基金重仓',
        '微盘股',
        '创业板综',
        'QFII重仓',
        '央国企改革',
        '专精特新',
        '标准普尔',
        '低价股',
        '昨日涨停',
        '昨日触板',
        '一带一路',
        '高股息精选',
        '含H股',
        '通达信88',
        '含B股',
        '上证50',
        '上证50_',  # 新增
        '上证180',  # 新增
        'ST板块',
        '昨日连板_含一字',
        '昨日连板',
        '沪股通',
        '深股通',
        '港股通',
        'MSCI',
        '标普道琼斯',
        '富时罗素',
        '参股新三板',  # 添加参股新三板
        '证金持股',  # 新增
        '同花顺漂亮100',  # 新增
        '同花顺中特估100',  # 新增
        '预亏预减',  # 新增
        '机构重仓',  # 新增
        '中证500',  # 新增
        '深成500',  # 新增
        '贬值受益',  # 新增
        '小盘非融',  # 新增
        'AH股'  # 新增
    }


def is_meaningful_concept(concept_name):
    """
    判断单个概念或板块是否有意义
    
    参数:
    - concept_name: 概念或板块名称
    
    返回:
    - bool: True表示有意义，False表示无意义
    """
    meaningless_items = get_meaningless_items()
    return concept_name not in meaningless_items


def filter_concept_list(concept_list):
    """
    过滤概念列表，移除无意义的概念
    
    参数:
    - concept_list: 概念列表
    
    返回:
    - list: 过滤后的概念列表
    """
    if not concept_list:
        return []
    
    meaningless_items = get_meaningless_items()
    return [concept for concept in concept_list if concept not in meaningless_items]


def filter_concept_dict(concept_dict):
    """
    过滤概念字典，移除无意义的概念键
    
    参数:
    - concept_dict: 概念字典
    
    返回:
    - dict: 过滤后的概念字典
    """
    if not concept_dict:
        return {}
    
    meaningless_items = get_meaningless_items()
    return {k: v for k, v in concept_dict.items() if k not in meaningless_items}


# 为了向后兼容，提供一个别名
filter_concepts = filter_meaningful_concepts_and_sectors


if __name__ == "__main__":
    # 测试代码
    test_concepts = [
        '人工智能', '昨日涨停', '芯片概念', '融资融券', 
        '新能源汽车', '央视50', '医疗器械', 'ST板块'
    ]
    
    print("原始概念列表:", test_concepts)
    filtered = filter_meaningful_concepts_and_sectors(test_concepts)
    print("过滤后概念列表:", filtered)
    
    test_dict = {
        '人工智能': 5,
        '昨日涨停': 3,
        '芯片概念': 8,
        '融资融券': 2
    }
    
    print("\n原始概念字典:", test_dict)
    filtered_dict = filter_meaningful_concepts_and_sectors(test_dict)
    print("过滤后概念字典:", filtered_dict)
